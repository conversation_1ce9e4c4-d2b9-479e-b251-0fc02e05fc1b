import streamlit as st
import pandas as pd
import random
import itertools
from datetime import date
from io import BytesIO

st.set_page_config(page_title="Exam Duty Manager", layout="wide")
st.title("🧾 Exam Duty Management System")

# Initialize session state
if "teachers" not in st.session_state:
    st.session_state.teachers = []
if "rooms" not in st.session_state:
    st.session_state.rooms = []
if "assignments" not in st.session_state:
    st.session_state.assignments = []
if "last_duty_df" not in st.session_state:
    st.session_state.last_duty_df = None

# Sidebar Input
st.sidebar.header("📥 Data Input")

# Upload Excel Option
uploaded_file = st.sidebar.file_uploader(
    "Upload Excel (with Teachers & Rooms)", type=["xlsx"]
)
if uploaded_file:
    df_teachers = pd.read_excel(uploaded_file, sheet_name="Teachers")
    df_rooms = pd.read_excel(uploaded_file, sheet_name="Rooms")
    st.session_state.teachers = df_teachers.to_dict("records")
    st.session_state.rooms = df_rooms.to_dict("records")
    st.sidebar.success("Excel data loaded successfully!")
else:
    with st.sidebar.expander("👨‍🏫 Add Teachers"):
        name = st.text_input("Teacher Name")
        department = st.text_input("Department")
        salutation = st.selectbox("Salutation", ["Mr.", "Ms."])
        if st.button("➕ Add Teacher"):
            st.session_state.teachers.append(
                {"name": name, "department": department, "salutation": salutation}
            )

    with st.sidebar.expander("🏫 Add Rooms"):
        room_name = st.text_input("Room Name")
        room_capacity = st.number_input("Room Capacity (Teachers)", min_value=1, step=1)
        if st.button("➕ Add Room"):
            existing_rooms = [room["name"] for room in st.session_state.rooms]
            if room_name not in existing_rooms:
                st.session_state.rooms.append(
                    {"name": room_name, "capacity": room_capacity}
                )
            else:
                st.warning("Room already added.")

# Display Input Summary
col1, col2 = st.columns(2)
with col1:
    st.subheader("👥 Teachers List")
    if st.session_state.teachers:
        st.dataframe(pd.DataFrame(st.session_state.teachers))
with col2:
    st.subheader("🏫 Rooms List")
    if st.session_state.rooms:
        st.dataframe(pd.DataFrame(st.session_state.rooms))

# Select Shift and Date
st.subheader("📅 Duty Configuration")
duty_date = st.date_input("Select Duty Date", value=date.today())
shift = st.selectbox("Select Shift", ["Morning", "Afternoon", "Evening"])

# Assignment Logic
if st.button("🎯 Generate Duty Assignments"):
    st.session_state.assignments = []
    available_teachers = st.session_state.teachers.copy()
    random.shuffle(available_teachers)

    def is_valid_group(group):
        depts = [t["department"] for t in group]
        if len(depts) != len(set(depts)):  # same department
            return False
        female_count = sum(1 for t in group if t["salutation"] == "Ms.")
        if female_count > 1:
            return False
        return True

    for room in st.session_state.rooms:
        room_name = room["name"]
        capacity = int(room["capacity"])
        best_group = []

        # Try combinations of teachers
        for size in range(capacity, 0, -1):
            possible_groups = list(itertools.combinations(available_teachers, size))
            random.shuffle(possible_groups)
            for group in possible_groups:
                if is_valid_group(group):
                    best_group = list(group)
                    break
            if best_group:
                break

        if best_group:
            st.session_state.assignments.append(
                {"room": room_name, "teachers": best_group}
            )
            for t in best_group:
                available_teachers.remove(t)

    st.success("✅ Duty assignment completed.")

# Display Results
if st.session_state.assignments:
    st.subheader(f"📋 Duty Chart for {duty_date} ({shift} Shift)")
    result_data = []
    for entry in st.session_state.assignments:
        room = entry["room"]
        teachers = ", ".join(
            [t["salutation"] + " " + t["name"] for t in entry["teachers"]]
        )
        result_data.append(
            {
                "Date": duty_date.strftime("%Y-%m-%d"),
                "Shift": shift,
                "Room": room,
                "Assigned Teachers": teachers,
            }
        )

    duty_df = pd.DataFrame(result_data)
    st.session_state.last_duty_df = duty_df  # Store for download
    st.dataframe(duty_df)

    # Download as Excel
    output = BytesIO()
    with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
        duty_df.to_excel(writer, index=False, sheet_name="Duty Chart")
    st.download_button(
        label="📥 Download Duty Chart as Excel",
        data=output.getvalue(),
        file_name=f"duty_chart_{duty_date}_{shift}.xlsx",
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )
